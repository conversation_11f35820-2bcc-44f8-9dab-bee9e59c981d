import { create } from "zustand";

export type CardData = {
  panMasked: string;
  panFull?: string;
  expiry?: string;   // YYMM
  scheme?: string;
};

type State = {
  card?: CardData;
  unlocked: boolean;
  setCard: (c: CardData) => void;
  lock: () => void;
  unlock: () => void;
};

export const useCardStore = create<State>((set) => ({
  card: undefined,
  unlocked: false,
  setCard: (c) => set({ card: c, unlocked: false }),
  lock: () => set({ unlocked: false }),
  unlock: () => set({ unlocked: true }),
}));
