import { ReactNode } from "react";
import { View, StyleSheet } from "react-native";
import { theme } from "../lib/theme";

export function Card({ children, padded = true }: { children: ReactNode; padded?: boolean }) {
  return <View style={[styles.card, padded && styles.padded]}>{children}</View>;
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.radii.lg,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "rgba(255,255,255,0.06)",
  },
  padded: {
    padding: theme.spacing(2),
  },
});
