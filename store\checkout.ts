import { create } from "zustand";

type Customer = {
  company?: string;
  name: string;
  phone: string;
  email: string;
  product?: string;
  note?: string;
  address?: string;
};

type Card = {
  holder?: string;
  number?: string;
  month?: string;
  year?: string;
  ccv?: string;
};

type State = {
  customer?: Customer;
  card?: Card;
  setCustomer: (c: Customer) => void;
  setCard: (c: Card) => void;
  clear: () => void;
};

export const useCheckout = create<State>((set) => ({
  customer: undefined,
  card: undefined,
  setCustomer: (c) => set({ customer: c }),
  setCard: (c) => set({ card: c }),
  clear: () => set({ customer: undefined, card: undefined }),
}));
