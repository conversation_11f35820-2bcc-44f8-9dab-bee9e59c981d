import Nfc<PERSON>anager, { NfcTech } from "react-native-nfc-manager";
import { parseTLV, findTLV } from "../emv/tlv";
import { selectAID, readRecord } from "../emv/apdu";

const KNOWN_AIDS = [
  "A0000000031010", // Visa
  "A0000000041010", // MasterCard
  "A00000002501",   // Amex
  "A0000003241010", // Discover
  "A0000002771010", // Interac
  "A0000006581010", // MIR
];

// Yardımcılar
const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));
const hexToBytes = (hex: string) => hex.match(/.{1,2}/g)!.map(b => parseInt(b,16));
const toHex = (bytes: number[]) => bytes.map(b => b.toString(16).padStart(2,'0')).join('').toUpperCase();
const SW = (resp: string) => resp.slice(-4).toUpperCase();

// APDU gönder + LOG
async function tx(name: string, hexCmd: string, delay = 80) {
  console.log(`APDU >> [${name}] ${hexCmd}`);
  const resp = toHex(await NfcManager.transceive(hexToBytes(hexCmd)));
  console.log(`APDU << [${name}] ${resp}`);
  await sleep(delay);
  return resp;
}

// PPSE: 2PAY.SYS.DDF01
const SELECT_PPSE = "00A404000E325041592E5359532E444446303100";

function extractAIDsFromPPSE(ppseFciHex: string): string[] {
  const tlvs = parseTLV(ppseFciHex);
  const dir = findTLV(tlvs, "BF0C");
  const out: string[] = [];
  if (dir?.children) {
    for (const ch of dir.children) {
      const aid = findTLV([ch], "4F");
      if (aid) out.push(aid.valueHex);
    }
  }
  return out;
}

// PDOL destekli GPO kurucu
function pdolTotalLen(pdol: string) {
  let i=0, sum=0;
  while (i<pdol.length) {
    // TAG
    let b = parseInt(pdol.substr(i,2),16); i+=2;
    if ((b & 0x1F) === 0x1F) { // çok baytlı tag
      let c = parseInt(pdol.substr(i,2),16); i+=2;
      while (c & 0x80) { c = parseInt(pdol.substr(i,2),16); i+=2; }
    }
    // LEN
    const L = parseInt(pdol.substr(i,2),16); i+=2;
    sum += L;
  }
  return sum;
}
function buildGPO(pdolHex?: string) {
  if (!pdolHex) return "80A8000002830000"; // 83 00
  const total = pdolTotalLen(pdolHex);
  const data = "00".repeat(total);
  const Lc   = (2 + total).toString(16).padStart(2,'0'); // 83 + dataLen
  const dLen = total.toString(16).padStart(2,'0');
  return `80A80000${Lc}83${dLen}${data}00`;
}

function schemeFromPAN(pan: string) {
  if (/^4/.test(pan)) return "VISA";
  if (/^5[1-5]/.test(pan) || /^2(2[2-9]|[3-6]\d|7[01])/.test(pan)) return "MASTERCARD";
  if (/^3[47]/.test(pan)) return "AMEX";
  return "CARD";
}

export async function readEmvCard(): Promise<{ panMasked:string; panFull?:string; expiry?:string; scheme?:string }> {
  await NfcManager.requestTechnology(NfcTech.IsoDep, { alertMessage: "Kartı yaklaştırın" } as any);
  try {
    // 1) PPSE
    const ppseResp = await tx("SELECT_PPSE", SELECT_PPSE);
    let aids: string[] = [];
    if (SW(ppseResp) === "9000") {
      aids = extractAIDsFromPPSE(ppseResp.slice(0, -4));
      console.log("PPSE AIDs:", aids);
    } else {
      console.log("PPSE başarısız, bilinen AID'lere geçilecek.");
    }

    // 2) AID seçimi (PPSE’den ya da bilinenlerden)
    let selectedAid: string | undefined;
    let selectResp = "";
    const candidates = aids.length ? aids : KNOWN_AIDS;
    for (const aid of candidates) {
      const r = await tx(`SELECT_AID_${aid}`, selectAID(aid));
      if (SW(r) === "9000") { selectedAid = aid; selectResp = r; break; }
    }
    if (!selectedAid) throw new Error("AID seçimi başarısız (PPSE + fallback).");

    // 3) PDOL kontrolü ve GPO
    const fciTlvs = parseTLV(selectResp.slice(0, -4));
    const pdol = findTLV(fciTlvs, "9F38")?.valueHex;
    const gpoCmd = buildGPO(pdol);
    const gpoResp = await tx("GET_PROCESSING_OPTIONS", gpoCmd);
    if (SW(gpoResp) !== "9000") throw new Error("GPO başarısız.");

    // 4) AFL -> READ RECORD
    const gpoTlvs = parseTLV(gpoResp.slice(0, -4));
    const afl = findTLV(gpoTlvs, "94")?.valueHex || "";
    if (!afl) throw new Error("AFL bulunamadı.");

    const records: Array<{sfi:number; rec:number}> = [];
    for (let i=0;i<afl.length;i+=8) {
      const sfi = (parseInt(afl.substr(i,2),16) >> 3);
      const first = parseInt(afl.substr(i+2,2),16);
      const last  = parseInt(afl.substr(i+4,2),16);
      for (let r=first; r<=last; r++) records.push({ sfi, rec:r });
    }
    console.log("AFL records:", records);

    let pan: string | undefined; let expiry: string | undefined;
    for (const r of records) {
      const rr = await tx(`READ_RECORD_${r.sfi}_${r.rec}`, readRecord(r.rec, r.sfi));
      if (SW(rr) !== "9000") continue;
      const tlvs = parseTLV(rr.slice(0, -4));
      const t5A = findTLV(tlvs, "5A");
      const t57 = findTLV(tlvs, "57");
      const t5F24 = findTLV(tlvs, "5F24");

      if (!pan && t5A) pan = t5A.valueHex.replace(/F+$/,"");
      if (!pan && t57) {
        const track = t57.valueHex;
        const d = track.indexOf("D");
        if (d > 0) pan = track.substring(0, d);
        if (!expiry && d > 0 && track.length >= d+5) expiry = track.substring(d+1, d+5); // YYMM
      }
      if (!expiry && t5F24) expiry = t5F24.valueHex.substring(0,4);

      if (pan && expiry) break;
    }

    if (!pan) throw new Error("PAN bulunamadı (kart kısıtlıyor olabilir).");
    const panMasked = "**** **** **** " + pan.slice(-4);
    return { panMasked, panFull: pan, expiry, scheme: schemeFromPAN(pan) };
  } finally {
    NfcManager.cancelTechnologyRequest().catch(()=>{});
  }
}
