{"expo": {"name": "NfcPaymentApp", "slug": "NfcPaymentApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "nfcpaymentapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.enesasilturk.NfcPaymentApp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA", "android.permission.NFC"], "package": "com.enesasilturk.NfcPaymentApp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/nlksoft-logo.png", "imageWidth": 220, "resizeMode": "contain", "backgroundColor": "#223873"}], "react-native-nfc-manager"], "experiments": {"typedRoutes": true}}}