import * as React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Login from './app/login';
import Register from './app/register';
import Customer from './app/customer';
import Payment from './app/payment';
import Verify from './app/verify';

export type RootStackParamList = {
  login: undefined;
  register: undefined;
  customer: undefined;
  payment: undefined;
  verify: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="login">
        <Stack.Screen name="login" component={Login} />
        <Stack.Screen name="register" component={Register} />
        <Stack.Screen name="customer" component={Customer} />
        <Stack.Screen name="payment" component={Payment} />
        <Stack.Screen name="verify" component={Verify} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
