import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Alert, Modal } from "react-native";
import { HeaderBar } from "../components/HeaderBar";
import { AppInput } from "../components/AppInput";
import { AppButton } from "../components/AppButton";
import { theme } from "../lib/theme";
import { useCheckout } from "../store/checkout";
import { useState, useCallback, useEffect } from "react";
import NfcManager, { NfcTech, Ndef } from "react-native-nfc-manager";
import { CameraKitCameraScreen } from "react-native-camera-kit";

export default function PaymentScreen() {
  const { customer, setCard } = useCheckout();
  const [holder, setHolder] = useState("");
  const [number, setNumber] = useState("");
  const [month, setMonth] = useState("");
  const [year, setYear] = useState("");
  const [ccv, setCcv] = useState("");

  const [hasPermission, setHasPermission] = useState<boolean | null>(true);
  const [scannerOpen, setScannerOpen] = useState(false);

  useEffect(() => {
    (async () => {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasPermission(status === "granted");
    })();
  }, []);

  const parseNdef = (tag: any) => {
    try {
      const records = tag?.ndefMessage || [];
      for (const rec of records) {
        const parsed = Ndef.text.decodePayload(rec.payload);
        if (parsed) {
          const obj = JSON.parse(parsed);
          if (obj.holder) setHolder(obj.holder);
          if (obj.number) setNumber(obj.number);
          if (obj.month) setMonth(String(obj.month));
          if (obj.year) setYear(String(obj.year));
          if (obj.ccv) setCcv(String(obj.ccv));
        }
      }
    } catch {}
  };

  const onScanNfc = async () => {
    try {
      await NfcManager.start();
      await NfcManager.requestTechnology(NfcTech.Ndef);
      const tag = await NfcManager.getTag();
      parseNdef(tag);
      Alert.alert("NFC", "Kart bilgileri okundu (NDEF).");
    } catch (e) {
      Alert.alert("NFC", "Okuma iptal edildi veya başarısız.");
    } finally {
      NfcManager.cancelTechnologyRequest().catch(() => {});
    }
  };

  const onBarCodeScanned = ({ data }: { data: string }) => {
    try {
      const obj = JSON.parse(data);
      if (obj.holder) setHolder(obj.holder);
      if (obj.number) setNumber(obj.number);
      if (obj.month) setMonth(String(obj.month));
      if (obj.year) setYear(String(obj.year));
      if (obj.ccv) setCcv(String(obj.ccv));
      setScannerOpen(false);
      Alert.alert("Kamera", "Kart bilgileri okundu (QR).");
    } catch {
      Alert.alert("Kamera", "Geçersiz kod.");
    }
  };

  const onPay = () => {
    if (!holder || !number || !month || !year || !ccv) return Alert.alert("Eksik", "Kart bilgilerini doldurun.");
    setCard({ holder, number, month, year, ccv });
    Alert.alert("Ödeme", "Demo: ödeme başarıyla simüle edildi.");
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : undefined} style={{ flex: 1, backgroundColor: theme.bg }}>
      <HeaderBar />
      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.title}>Ödeme</Text>
        <Text style={styles.subtitle}>Kart Bilgileri*</Text>

        <AppInput placeholder="Kart Üzerindeki İsim" value={holder} onChangeText={setHolder} />
        <AppInput placeholder="Kart Numarası" keyboardType="number-pad" value={number} onChangeText={setNumber} />

        <View style={styles.row3}>
          <View style={{ flex: 1 }}><AppInput placeholder="Ay (AA)" keyboardType="number-pad" value={month} onChangeText={setMonth} /></View>
          <View style={{ width: 12 }} />
          <View style={{ flex: 1 }}><AppInput placeholder="Yıl (YY)" keyboardType="number-pad" value={year} onChangeText={setYear} /></View>
          <View style={{ width: 12 }} />
          <View style={{ flex: 1 }}><AppInput placeholder="CCV*" keyboardType="number-pad" value={ccv} onChangeText={setCcv} /></View>
        </View>

        <Text style={[styles.subtitle, { marginTop: theme.spacing(2) }]}>Kart Ekleme Yöntemleri*</Text>
        <View style={styles.row2}>
          <AppButton title="NFC ile Tara" onPress={onScanNfc} variant="outline" style={{ flex: 1 }} />
          <View style={{ width: 12 }} />
          <AppButton title="Kartı Tara" onPress={() => setScannerOpen(true)} variant="outline" style={{ flex: 1 }} />
        </View>

        <View style={styles.cardPreview}>
          <Text style={styles.cardNumber}>{number ? number.replace(/\d(?=\d{4})/g, "#") : "####################"}</Text>
          <Text style={styles.cardName}>{holder || "İsim Soyisim"}</Text>
        </View>

        <View style={styles.row2}>
          <AppButton title="Ödeme Yap" onPress={onPay} style={{ flex: 1 }} />
          <View style={{ width: 12 }} />
          <AppButton title="BKM Express ile Öde" onPress={() => Alert.alert("BKM", "BKM Express entegrasyonu örnek projenize eklenecek.")} variant="outline" style={{ flex: 1 }} />
        </View>
      </ScrollView>

      <Modal visible={scannerOpen} animationType="slide">
        <View style={{ flex: 1 }}>
          <CameraKitCameraScreen onReadCode={(e) => onBarCodeScanned({ data: e?.nativeEvent?.codeStringValue || "" })} showFrame={true} scanBarcode={true} style={{ flex: 1 }} />
          <AppButton title="Kapat" onPress={() => setScannerOpen(false)} style={{ position: "absolute", bottom: 24, left: 24, right: 24 }} />
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: { padding: theme.spacing(2), gap: theme.spacing(2) },
  title: { fontSize: 28, fontWeight: "800", color: theme.text, marginVertical: theme.spacing(1) },
  subtitle: { fontSize: 16, fontWeight: "700", color: theme.text },
  row2: { flexDirection: "row", alignItems: "center", marginTop: 8 },
  row3: { flexDirection: "row", alignItems: "center", marginTop: 8 },
  cardPreview: { backgroundColor: "#f1f5f9", padding: 16, borderRadius: 16, marginVertical: 12 },
  cardNumber: { fontSize: 18, fontWeight: "700", letterSpacing: 2, color: "#0f172a" },
  cardName: { marginTop: 6, color: "#334155" },
});
