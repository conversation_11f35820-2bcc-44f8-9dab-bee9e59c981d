import React, { useMemo, useState } from "react";
import { View, Text, TextInput, Button, StyleSheet, Alert } from "react-native";
import { useCardStore } from "../src/store/useCardStore";

export default function VerifyScreen() {
  const { card, unlocked, unlock, lock } = useCardStore();
  const [last4, setLast4] = useState("");

  const hint = useMemo(() => card?.panMasked?.slice(-4) ?? "----", [card]);

  if (!card) {
    return <View style={styles.wrap}><Text>Kart verisi yok. Lütfen önce kart okut.</Text></View>;
  }

  const onVerify = () => {
    if (last4 === hint) {
      unlock();
      Alert.alert("Başarılı", "Bilgilerin kilidi açıldı.");
    } else {
      lock();
      Alert.alert("Hatalı", "Son 4 hane yanlış.");
    }
  };

  return (
    <View style={styles.wrap}>
      <Text style={styles.title}>Kart Bilgileri</Text>
      <Text style={styles.row}>Kart: {card.scheme ?? "Kart"} ({card.panMasked})</Text>
      <Text style={styles.row}>SKT: {card.expiry ? `20${card.expiry.slice(0,2)}/${card.expiry.slice(2,4)}` : "—"}</Text>

      <View style={{height:16}} />

      <Text style={styles.sub}>Kilidi Aç (Son 4 Hane)</Text>
      <TextInput
        value={last4}
        onChangeText={setLast4}
        keyboardType="number-pad"
        maxLength={4}
        placeholder="1234"
        style={styles.input}
      />
      <Button title="Doğrula" onPress={onVerify} />

      <View style={{height:20}} />

      {unlocked ? (
        <View style={styles.box}>
          <Text style={styles.boxTitle}>Erişilebilir Bilgiler</Text>
          <Text>Tam PAN (DEMO): {card.panFull}</Text>
          <Text>Şema: {card.scheme}</Text>
        </View>
      ) : (
        <Text style={{opacity:0.7}}>Erişim kilitli. Doğru son 4 haneyi gir.</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  wrap: { flex:1, padding:20, gap:8 },
  title: { fontSize:20, fontWeight:"700" },
  sub: { fontSize:16, fontWeight:"600" },
  row: { fontSize:16 },
  input: { borderWidth:1, borderRadius:8, paddingHorizontal:12, paddingVertical:8 },
  box: { borderWidth:1, borderRadius:12, padding:12, marginTop:8 },
  boxTitle: { fontWeight:"700", marginBottom:6 }
});
