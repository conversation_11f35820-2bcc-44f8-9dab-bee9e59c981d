{"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg", "reactNativePath": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native", "dependencies": {"react-native-gesture-handler": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-nfc-manager": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-nfc-manager", "name": "react-native-nfc-manager", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-nfc-manager\\android", "packageImportPath": "import community.revteltech.nfc.NfcManagerPackage;", "packageInstance": "new NfcManagerPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-nfc-manager/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-picker/picker": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\@react-native-picker\\picker", "name": "@react-native-picker/picker", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\@react-native-picker\\picker\\android", "packageImportPath": "import com.reactnativecommunity.picker.RNCPickerPackage;", "packageInstance": "new RNCPickerPackage()", "buildTypes": [], "libraryName": "rnpicker", "componentDescriptors": ["RNCAndroidDialogPickerComponentDescriptor", "RNCAndroidDropdownPickerComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-camera-kit": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-camera-kit", "name": "react-native-camera-kit", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-camera-kit\\android", "packageImportPath": "import com.rncamerakit.RNCameraKitPackage;", "packageInstance": "new RNCameraKitPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Downloads/NfcPaymentApp-bareRN-fixed-pkg/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.enesasilturk.NfcPaymentApp", "sourceDir": "C:\\Users\\<USER>\\Downloads\\NfcPaymentApp-bareRN-fixed-pkg\\android"}}}