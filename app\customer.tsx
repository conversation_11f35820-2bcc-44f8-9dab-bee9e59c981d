import { View, Text, StyleSheet, ScrollView, KeyboardAvoidingView, Platform, Alert } from "react-native";
import { HeaderBar } from "../components/HeaderBar";
import { AppInput } from "../components/AppInput";
import { AppButton } from "../components/AppButton";
import { theme } from "../lib/theme";
import { useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { useCheckout } from "../store/checkout";
import { Picker } from "@react-native-picker/picker";

export default function CustomerScreen() {
  const navigation = useNavigation<any>();
  const setCustomer = useCheckout((s) => s.setCustomer);

  const [company, setCompany] = useState("");
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [product, setProduct] = useState<string | undefined>(undefined);
  const [note, setNote] = useState("");
  const [address, setAddress] = useState("");

  const onNext = () => {
    if (!name || !phone || !email || !product || !note || !address) {
      return Alert.alert("Eksik Alan", "Yıldızlı alanları doldurunuz.");
    }
    setCustomer({ company, name, phone, email, product, note, address });
    navigation.navigate("payment");
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : undefined} style={{ flex: 1, backgroundColor: theme.bg }}>
      <HeaderBar />
      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.title}>Müşteri Bilgileri</Text>

        <AppInput placeholder="Firma Adı" value={company} onChangeText={setCompany} />

        <View style={styles.row2}>
          <View style={{ flex: 1 }}><AppInput placeholder="Müşteri Adı*" value={name} onChangeText={setName} /></View>
          <View style={{ width: 12 }} />
          <View style={{ flex: 1 }}><AppInput placeholder="Telefon No*" keyboardType="phone-pad" value={phone} onChangeText={setPhone} /></View>
        </View>

        <AppInput placeholder="E-Posta Adresi*" keyboardType="email-address" autoCapitalize="none" value={email} onChangeText={setEmail} />

        <View style={styles.pickerWrap}>
          <Text style={styles.pickerLabel}>Ürün/Hizmet Seç*</Text>
          <View style={styles.pickerBox}>
            <Picker selectedValue={product} onValueChange={(v) => setProduct(String(v))}>
              <Picker.Item label="Seçiniz" value={undefined} />
              <Picker.Item label="Hizmet A" value="A" />
              <Picker.Item label="Hizmet B" value="B" />
              <Picker.Item label="Hizmet C" value="C" />
            </Picker>
          </View>
        </View>

        <AppInput placeholder="Notunuz*" value={note} onChangeText={setNote} />
        <AppInput placeholder="Adresiniz*" value={address} onChangeText={setAddress} />

        <AppButton title="Ödeme Ekranına git" onPress={onNext} style={{ marginTop: theme.spacing(2) }} />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: { padding: theme.spacing(2), gap: theme.spacing(2) },
  title: { fontSize: 28, fontWeight: "800", color: theme.text, marginVertical: theme.spacing(1) },
  row2: { flexDirection: "row", alignItems: "center" },
  pickerWrap: { gap: 6 },
  pickerLabel: { color: theme.text, fontWeight: "700", fontSize: 20 },
  pickerBox: { borderWidth: 1.5, borderColor: theme.inputBorder, borderRadius: 16, overflow: "hidden" },
});
