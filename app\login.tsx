import { View, Text, StyleSheet, Alert, KeyboardAvoidingView, Platform, ScrollView } from "react-native";
import { HeaderBar } from "../components/HeaderBar";
import { AppInput } from "../components/AppInput";
import { AppButton } from "../components/AppButton";
import { theme } from "../lib/theme";
import { useState } from "react";
import { useAuth } from "../store/auth";
import { useNavigation } from "@react-navigation/native";

export default function LoginScreen() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const login = useAuth((s) => s.login);
  const navigation = useNavigation<any>();

  const onSubmit = async () => {
    const ok = await login(email.trim(), password);
    if (!ok) return Alert.alert("Hata", "Lütfen e-posta ve şifreyi kontrol edin.");
    navigation.reset({ index: 0, routes: [{ name: "customer" }] });
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : undefined} style={{ flex: 1, backgroundColor: theme.bg }}>
      <HeaderBar />
      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.title}>Giriş Yap</Text>
        <View style={{ gap: theme.spacing(2) }}>
          <AppInput placeholder="E-posta" keyboardType="email-address" autoCapitalize="none" value={email} onChangeText={setEmail} />
          <AppInput placeholder="Şifre" secureTextEntry value={password} onChangeText={setPassword} />
        </View>

        <AppButton title="Giriş Yap" onPress={onSubmit} style={{ marginTop: theme.spacing(3) }} />

        <View style={styles.row}>
          <Text style={styles.boldRed}>Hesabınız yok mu?</Text>
          /* @ts-ignore */<Text onPress={() => navigation.navigate("register")} style={styles.link}>Kayıt Ol</Text>
        </View>
        <Text style={styles.forgot}>Şifremi Unuttum</Text>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: { padding: theme.spacing(2), gap: theme.spacing(3) },
  title: { fontSize: 32, fontWeight: "800", color: theme.text, marginVertical: theme.spacing(1) },
  row: { flexDirection: "row", alignItems: "center", gap: 10, marginTop: theme.spacing(2) },
  boldRed: { color: "#e11d2e", fontSize: 18, fontWeight: "700" },
  link: { color: theme.brandBlue, fontSize: 18, fontWeight: "700" },
  forgot: { color: theme.brandBlue, marginTop: theme.spacing(1) },
});
