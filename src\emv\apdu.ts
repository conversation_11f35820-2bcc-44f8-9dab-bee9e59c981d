export const SELECT_PPSE = "00A404000E325041592E5359532E444446303100"; // 2PAY.SYS.DDF01
export const GET_PROCESSING_OPTIONS = "80A8000002830000";

export function selectAID(aidHex: string) {
  const len = (aidHex.length/2).toString(16).padStart(2,'0');
  return `00A40400${len}${aidHex}00`;
}
export function readRecord(record: number, sfi: number) {
  const p2 = ((sfi << 3) | 4).toString(16).padStart(2,'0');
  const p1 = record.toString(16).padStart(2,'0');
  return `00B2${p1}${p2}00`;
}
