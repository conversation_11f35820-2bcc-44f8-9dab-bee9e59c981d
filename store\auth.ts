import { create } from "zustand";

type User = { id: string; name: string; email: string };
type AuthState = {
  user?: User;
  token?: string;
  login: (email: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoggedIn: boolean;
};

export const useAuth = create<AuthState>((set, get) => ({
  user: undefined,
  token: undefined,
  isLoggedIn: false,
  async login(email, password) {
    // Demo: accept any email + >=4 char password
    if (!email || (password?.length ?? 0) < 4) return false;
    set({ user: { id: "1", name: "<PERSON><PERSON><PERSON><PERSON>ı", email }, token: "demo-token", isLoggedIn: true });
    return true;
  },
  async register(name, email, password) {
    if (!name || !email || (password?.length ?? 0) < 4) return false;
    set({ user: { id: "1", name, email }, token: "demo-token", isLoggedIn: true });
    return true;
  },
  logout() { set({ user: undefined, token: undefined, isLoggedIn: false }); }
}));
