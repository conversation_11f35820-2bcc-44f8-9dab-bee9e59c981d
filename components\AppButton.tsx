import { TouchableOpacity, Text, StyleSheet, ViewStyle } from "react-native";
import { theme } from "../lib/theme";

type Props = {
  title: string;
  onPress?: () => void;
  variant?: "primary" | "outline" | "danger";
  style?: ViewStyle;
  disabled?: boolean;
};

export function AppButton({ title, onPress, variant="primary", style, disabled }: Props) {
  return (
    <TouchableOpacity
      accessibilityRole="button"
      onPress={onPress}
      disabled={disabled}
      style={[styles.base, variant === "primary" ? styles.primary : styles.outline, disabled && { opacity: 0.6 }, style]}
    >
      <Text style={[styles.text, variant === "primary" ? styles.textPrimary : styles.textOutline]}>{title}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  base: {
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  primary: {
    backgroundColor: theme.red,
  },
  outline: {
    borderColor: theme.brandBlue,
    borderWidth: 1.5,
    backgroundColor: "transparent",
  },
  text: { fontSize: 18, fontWeight: "700" },
  textPrimary: { color: "#fff" },
  textOutline: { color: theme.brandBlue },
});
