import { View, Image, StyleSheet, Platform } from "react-native";
import { theme } from "../lib/theme";

export function HeaderBar() {
  return (
    <View style={styles.container}>
      <Image source={require("../assets/images/nlksoft-logo.png")} style={styles.logo} resizeMode="contain" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.brandBlue,
    paddingTop: Platform.OS === "ios" ? 54 : 28,
    paddingBottom: 12,
    paddingHorizontal: theme.spacing(2),
  },
  logo: {
    width: 140, height: 40,
  },
});
