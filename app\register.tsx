import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Alert } from "react-native";
import { HeaderBar } from "../components/HeaderBar";
import { AppInput } from "../components/AppInput";
import { AppButton } from "../components/AppButton";
import { theme } from "../lib/theme";
import { useState } from "react";
import { useAuth } from "../store/auth";
import { useNavigation } from "@react-navigation/native";

export default function RegisterScreen() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const register = useAuth((s) => s.register);
  const navigation = useNavigation<any>();

  const onSubmit = async () => {
    const ok = await register(name.trim(), email.trim(), password);
    if (!ok) return Alert.alert("Hata", "<PERSON>ü<PERSON> alan<PERSON> doldurun ve güçlü bir şifre girin.");
    navigation.reset({ index: 0, routes: [{ name: "customer" }] });
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : undefined} style={{ flex: 1, backgroundColor: theme.bg }}>
      <HeaderBar />
      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.title}>Kayıt Ol</Text>
        <View style={{ gap: theme.spacing(2) }}>
          <AppInput placeholder="Ad Soyad" value={name} onChangeText={setName} />
          <AppInput placeholder="E-posta" keyboardType="email-address" autoCapitalize="none" value={email} onChangeText={setEmail} />
          <AppInput placeholder="Şifre" secureTextEntry value={password} onChangeText={setPassword} />
        </View>
        <AppButton title="Kayıt Ol" onPress={onSubmit} style={{ marginTop: theme.spacing(3) }} />
        <View style={styles.row}>
          <Text style={styles.boldRed}>Zaten Hesabın var mı?</Text>
          /* @ts-ignore */<Text onPress={() => navigation.navigate("login")} style={styles.link}>Giriş Yap</Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: { padding: theme.spacing(2), gap: theme.spacing(3) },
  title: { fontSize: 32, fontWeight: "800", color: theme.text, marginVertical: theme.spacing(1) },
  row: { flexDirection: "row", alignItems: "center", gap: 10, marginTop: theme.spacing(2) },
  boldRed: { color: "#e11d2e", fontSize: 18, fontWeight: "700" },
  link: { color: theme.brandBlue, fontSize: 18, fontWeight: "700" },
});
