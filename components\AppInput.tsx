import { TextInput, StyleSheet, View, Text, TextInputProps } from "react-native";
import { theme } from "../lib/theme";

type Props = TextInputProps & { label?: string; error?: string; };
export function AppInput({ label, error, ...rest }: Props) {
  return (
    <View style={{ gap: 6 }}>
      {label ? <Text style={styles.label}>{label}</Text> : null}
      <TextInput
        placeholderTextColor="#9aa0a6"
        style={[styles.input, error && { borderColor: "#ef4444" }]}
        {...rest}
      />
      {error ? <Text style={styles.error}>{error}</Text> : null}
    </View>
  );
}

const styles = StyleSheet.create({
  label: {
    color: theme.text,
    fontWeight: "700",
    fontSize: 20,
  },
  input: {
    borderWidth: 1.5,
    borderColor: theme.inputBorder,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  error: { color: "#ef4444" },
});
