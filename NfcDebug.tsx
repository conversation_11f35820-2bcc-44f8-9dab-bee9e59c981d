import React, { useState } from "react";
import { View, Text, Button, Alert, StyleSheet, ScrollView } from "react-native";
import NfcManager, { NfcTech } from "react-native-nfc-manager";

export default function NfcDebug() {
  const [last, setLast] = useState<string>("");

  const probe = async () => {
    try {
      await NfcManager.start();
      // Önce EMV kartlar için IsoDep dene
      await NfcManager.requestTechnology(NfcTech.IsoDep);
      const tag = await NfcManager.getTag();
      setLast(JSON.stringify(tag, null, 2));
      Alert.alert("Bulundu", `Techs: ${(tag?.techTypes || []).join(", ")}`);
    } catch (eIso: any) {
      // Olmadıysa NDEF dene
      try {
        await NfcManager.requestTechnology(NfcTech.Ndef);
        const tag = await NfcManager.getTag();
        setLast(JSON.stringify(tag, null, 2));
        Alert.alert("NDEF Bulundu", `Techs: ${(tag?.techTypes || []).join(", ")}`);
      } catch (eNdef: any) {
        Alert.alert("Hiç Tag Yok", String(eIso?.message || eNdef?.message || eIso || eNdef));
      } finally {
        NfcManager.cancelTechnologyRequest().catch(() => {});
      }
      return;
    }
    NfcManager.cancelTechnologyRequest().catch(() => {});
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Button title="NFC Test (IsoDep→NDEF)" onPress={probe} />
      <Text selectable style={styles.text}>{last}</Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flexGrow: 1, padding: 16 },
  text: { marginTop: 12, fontSize: 12 }
});
