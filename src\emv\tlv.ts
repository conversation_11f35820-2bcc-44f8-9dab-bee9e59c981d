export type TLV = { tag: string; length: number; valueHex: string; children?: TLV[] };

export function hexToBytes(hex: string): Uint8Array {
  const clean = hex.replace(/\s+/g,'');
  const arr = new Uint8Array(clean.length/2);
  for (let i=0;i<clean.length;i+=2) arr[i/2] = parseInt(clean.substr(i,2),16);
  return arr;
}
function toHex(bytes: number[] | Uint8Array) {
  return Array.from(bytes).map(b => b.toString(16).padStart(2,'0')).join('').toUpperCase();
}
function readLen(view: Uint8Array, i: number): [number, number] {
  let len = view[i++];
  if (len & 0x80) {
    const n = len & 0x7F; len = 0;
    for (let j=0;j<n;j++) len = (len<<8) | view[i++];
  }
  return [len, i];
}
export function parseTLV(hex: string): TLV[] {
  const data = hexToBytes(hex); const res: TLV[] = []; let i = 0;
  while (i < data.length) {
    let tag = data[i++].toString(16).padStart(2,'0').toUpperCase();
    if ((parseInt(tag,16) & 0x1F) === 0x1F) {
      let b = data[i++]; tag += b.toString(16).padStart(2,'0').toUpperCase();
      while (b & 0x80) { b = data[i++]; tag += b.toString(16).padStart(2,'0').toUpperCase(); }
    }
    let len: number; [len, i] = readLen(data, i);
    const val = data.slice(i, i+len); i += len;
    const constructed = (parseInt(tag,16) & 0x20) === 0x20;
    const tlv: TLV = { tag: tag.toUpperCase(), length: len, valueHex: toHex(val) };
    if (constructed) tlv.children = parseTLV(tlv.valueHex);
    res.push(tlv);
  }
  return res;
}
export function findTLV(tlvs: TLV[], tag: string): TLV | undefined {
  const TT = tag.toUpperCase();
  for (const t of tlvs) {
    if (t.tag === TT) return t;
    if (t.children) {
      const c = findTLV(t.children, TT);
      if (c) return c;
    }
  }
}
